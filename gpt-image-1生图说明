图片生成（文生图）
入参
{
  "maxTokens": 100,
  "messages": [
    {
      "content": [
        {
          "text": "A girl walking under the rainbow", // 与content二选一，如果同时传，优选取text
          "content": "A girl walking under the rainbow",
          "type": "text",
          "external": {
            "images": {
              "size": "1024x1024",
              "quality": "medium",
              "output_compression": 100,
              "output_format": "png",
              "n": 1
            }
          }
        }
      ],
      "role": "user"
    }
  ],
  "model": "gpt-image-1",
  "stream": false
}
出参
{
  "status": "000000",
  "desc": "success",
  "detail": {
    "id": null,
    "object": null,
    "created": 1752153971,
    "model": "gpt-image-1",
    "choices": [
      {
        "message": {
          "content": {
            "type": "image",
            "url": null,
            "content": null,
            "external": null,
            "text": null,
            "image_url": {
              "url": null,
              "detail": "image base64",
              "media_type": null
            },
            "request": null
          }
        }
      }
    ],
    "usage": {
      "promptTokens": 12,
      "completionTokens": 272,
      "totalTokens": 284
    },
    "allUsage": null,
    "system_fingerprint": null,
    "candidates": null,
    "promptFeedback": null
  },
  "traceId": ""
}


图片修改（图生图）
入参
{
  "maxTokens": 100,
  "messages": [
    {
      "content": [
        {
          "type": "image",
          "content": "image base64",
          "external": {
            "images": {
              "mask": "mask image base64", // 可选
              "format": "png" // 可选，默认png，上传的image and mask image format，"png"等格式名称遵循HTTP content type规范：image/png
            }
          }
        },
        {
          "type": "text",
          "content": "prompt"
        }
      ],
      "role": "user"
    }
  ],
  "model": "gpt-image-1-edit",
  "stream": false
}
出参
{
  "status": "000000",
  "desc": "success",
  "detail": {
    "id": null,
    "object": null,
    "created": 1752483174,
    "model": "gpt-image-1-edit",
    "choices": [
      {
        "message": {
          "content": {
            "type": "image",
            "url": null,
            "content": null,
            "contentList": null,
            "external": null,
            "text": null,
            "image_url": {
              "url": null,
              "detail": "image base64",
              "media_type": "image/png"
            },
            "request": null
          }
        }
      }
    ],
    "usage": {
      "promptTokens": 218,
      "completionTokens": 4160,
      "totalTokens": 4378
    },
    "allUsage": null,
    "usageDetail": {
      "promptTokens": 218,
      "completionTokens": 4160,
      "totalTokens": 4378,
      "promptTokensDetail": {
        "textTokens": 24,
        "imageTokens": 194
      }
    },
    "system_fingerprint": null,
    "candidates": null,
    "promptFeedback": null
  },
  "traceId": ""
}


