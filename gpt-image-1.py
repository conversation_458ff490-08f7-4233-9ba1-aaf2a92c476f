import requests
import random
import hashlib
import time
import string


class AIGCProvider:
    URL = "https://aigc-api.apps-hangyan.danlu.netease.com/api/v2/text/chat"

    APP_ID = "69e96233-0ba3-4373-91c9-edc2eb503409"
    APP_KEY = "7v678vsvucf42jtejagtdg2vs2ksld"

    def signed_headers(self, app_id: str, app_key: str) -> dict[str, str]:
        """
        生成签名过的请求头
        Args:
            app_id: 应用ID
            app_key: 应用密钥
        Returns:
            Dict[str, str]: 请求头字典
        """
        # 生成10位随机字符串（字母+数字）
        nonce = "".join(random.choices(string.ascii_letters + string.digits, k=10))
        # 获取当前时间戳（秒）
        timestamp = str(int(time.time()))
        # 构造待签名字符串
        str_to_sign = f"appId={app_id}&nonce={nonce}&timestamp={timestamp}&appkey={app_key}"
        # MD5 加密并转大写
        sign = hashlib.md5(str_to_sign.encode("utf-8")).hexdigest().upper()
        # 构造请求头
        headers = {
            "appId": app_id,
            "nonce": nonce,
            "timestamp": timestamp,
            "sign": sign,
            "version": "v2",
            "projectId": "ai-game",
            "Cntent-Type": "application/json",
        }
        return headers
    
    def text_to_image_gptimage(self, prompt: str, width: int = 1024, height: int = 1024):
        data = {
            "messages":
            [
                {
                    "content":
                    [
                        {
                            "text": prompt,
                            "type": "text",
                            "external": {
                                "images": {
                                    "prompt": "",
                                    "size": f"{width}x{height}",
                                    "output_format": "png",
                                }
                            }
                        }
                    ],
                    "role": "user"
                }
            ],
            "model": "gpt-image-1",
            "stream": False
        }
        response = requests.post(
            self.URL,
            headers=self.signed_headers(self.APP_ID, self.APP_KEY),
            json=data,
        )
        return response.json()['detail']['choices'][0]['message']['content']['image_url']['detail']


cls = AIGCProvider()
res = cls.text_to_image_gptimage('画一个圆滚滚的小猫咪')
print(res)